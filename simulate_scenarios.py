import numpy as np
from scipy.integrate import odeint
from scipy.optimize import minimize
import pandas as pd

def seizure_pharmacokinetic_model(state, t, params):
    A_phe_abs, A_phe_free, A_phe_bound, A_val_abs, A_val_free, A_val_bound, A_alb, Effect = state
    ka_phe = params["ka_phe"]
    ka_val = params["ka_val"]
    Vd = params["Vd"]
    CL_phe_normal = params["CL_phe_normal"]
    CL_val_normal = params["CL_val_normal"]
    CL_protein_loss = params["CL_protein_loss"]
    Kd_phe = params["Kd_phe"]
    Kd_val = params["Kd_val"]
    k_alb_in = params["k_alb_in"]
    k_alb_loss = params["k_alb_loss"]
    k_alb_syn = params["k_alb_syn"]
    ke0 = params["ke0"]
    C_phe_free = A_phe_free / Vd if A_phe_free > 0 else 0
    C_val_free = A_val_free / Vd if A_val_free > 0 else 0
    C_alb = A_alb / Vd
    f_free_phe = Kd_phe / (Kd_phe + C_alb * 1000)
    f_free_val = Kd_val / (Kd_val + C_alb * 1000)
    f_free_phe = max(0.01, min(0.99, f_free_phe))
    f_free_val = max(0.01, min(0.99, f_free_val))
    k_bind = 50
    A_phe_total = A_phe_free + A_phe_bound
    A_val_total = A_val_free + A_val_bound
    A_phe_free_target = A_phe_total * f_free_phe
    A_phe_bound_target = A_phe_total * (1 - f_free_phe)
    A_val_free_target = A_val_total * f_free_val
    A_val_bound_target = A_val_total * (1 - f_free_val)
    dA_phe_abs_dt = -ka_phe * A_phe_abs
    dA_val_abs_dt = -ka_val * A_val_abs
    dA_phe_free_dt = (ka_phe * A_phe_abs + k_bind * (A_phe_free_target - A_phe_free) - (CL_phe_normal + CL_protein_loss) * C_phe_free)
    dA_val_free_dt = (ka_val * A_val_abs + k_bind * (A_val_free_target - A_val_free) - (CL_val_normal + CL_protein_loss) * C_val_free)
    dA_phe_bound_dt = (k_bind * (A_phe_bound_target - A_phe_bound) - CL_protein_loss * (A_phe_bound / Vd))
    dA_val_bound_dt = (k_bind * (A_val_bound_target - A_val_bound) - CL_protein_loss * (A_val_bound / Vd))
    dA_alb_dt = k_alb_in + k_alb_syn - k_alb_loss * A_alb
    combined_free_conc = C_phe_free + C_val_free
    dEffect_dt = ke0 * (combined_free_conc - Effect)
    return [dA_phe_abs_dt, dA_phe_free_dt, dA_phe_bound_dt, dA_val_abs_dt, dA_val_free_dt, dA_val_bound_dt, dA_alb_dt, dEffect_dt]

def simulate_multiple_doses(dose_phe, dose_val, interval, duration, params):
    dt = 0.1
    t_points = np.arange(0, duration + dt, dt)
    initial_state = [0, 0, 0, 0, 0, 0, 35 * params["Vd"], 0]
    dose_times = np.arange(0, duration, interval)
    all_times = []
    all_states = []
    current_state = initial_state
    last_time = 0
    for i, dose_time in enumerate(dose_times):
        if dose_time > last_time:
            t_segment = np.arange(last_time, dose_time + dt, dt)
            if len(t_segment) > 1:
                states = odeint(seizure_pharmacokinetic_model, current_state, t_segment, args=(params,))
                all_times.extend(t_segment[:-1])
                all_states.extend(states[:-1])
                current_state = states[-1]
        current_state[0] += dose_phe
        current_state[3] += dose_val
        last_time = dose_time
    if last_time < duration:
        t_final = np.arange(last_time, duration + dt, dt)
        if len(t_final) > 1:
            states = odeint(seizure_pharmacokinetic_model, current_state, t_final, args=(params,))
            all_times.extend(t_final)
            all_states.extend(states)
    return np.array(all_times), np.array(all_states)

def seizure_risk_score(effect_concentration, threshold=0.1, steepness=100):
    return 1 / (1 + np.exp(steepness * (effect_concentration - threshold)))

def dose_optimization_objective(doses, target_params):
    dose_phe, dose_val = doses
    params = target_params["params"]
    interval = target_params["interval"]
    duration = target_params["duration"]
    times, states = simulate_multiple_doses(dose_phe, dose_val, interval, duration, params)
    effect_conc = states[:, 7]
    seizure_risks = [seizure_risk_score(conc) for conc in effect_conc]
    avg_seizure_risk = np.mean(seizure_risks)
    toxicity_penalty = 0.001 * (max(0, dose_phe - 600)**2 + max(0, dose_val - 1000)**2)
    max_free_phe = np.max(states[:, 1]) / params["Vd"]
    max_free_val = np.max(states[:, 4]) / params["Vd"]
    peak_penalty = 0.01 * (max(0, max_free_phe - 20)**2 + max(0, max_free_val - 100)**2)
    return avg_seizure_risk + toxicity_penalty + peak_penalty

# Model parameters
clinical_params = {
    "ka_phe": 0.6,
    "ka_val": 1.8,
    "Vd": 50,
    "CL_phe_normal": 2.2,
    "CL_val_normal": 4.5,
    "CL_protein_loss": 12.0,
    "Kd_phe": 8.0,
    "Kd_val": 45.0,
    "k_alb_in": 18.0,
    "k_alb_loss": 0.12,
    "k_alb_syn": 7.5,
    "ke0": 0.25,
}

def run_simulation_scenario(scenario_name, params, dose_phe, dose_val, interval, duration):
    t, states = simulate_multiple_doses(dose_phe, dose_val, interval, duration, params)
    effect_conc = states[:, 7]
    albumin_conc = states[:, 6] / params["Vd"]
    phe_free_conc = states[:, 1] / params["Vd"]
    val_free_conc = states[:, 4] / params["Vd"]
    phe_total_conc = (states[:, 1] + states[:, 2]) / params["Vd"]
    val_total_conc = (states[:, 4] + states[:, 5]) / params["Vd"]
    seizure_risks = [seizure_risk_score(conc) for conc in effect_conc]
    avg_seizure_risk = np.mean(seizure_risks)

    results_df = pd.DataFrame({
        "Time": t,
        "Effect_Concentration": effect_conc,
        "Albumin_Concentration": albumin_conc,
        "Phenytoin_Free_Concentration": phe_free_conc,
        "Valproate_Free_Concentration": val_free_conc,
        "Phenytoin_Total_Concentration": phe_total_conc,
        "Valproate_Total_Concentration": val_total_conc,
        "Seizure_Risk": seizure_risks
    })
    results_df.to_csv(f"results_{scenario_name}.csv", index=False)
    return avg_seizure_risk

if __name__ == "__main__":
    duration = 96
    interval = 12

    # Scenario 1: Standard Dosing
    dose_phe_standard = 300
    dose_val_standard = 500
    avg_risk_std = run_simulation_scenario("standard_dosing", clinical_params, dose_phe_standard, dose_val_standard, interval, duration)
    print(f"Standard Dosing - Average Seizure Risk: {avg_risk_std:.3f}")

    # Scenario 2: Optimized Dosing
    optimization_params = {
        "params": clinical_params,
        "interval": interval,
        "duration": duration
    }
    initial_doses = [450, 750]
    bounds = [(200, 800), (300, 1200)]
    result = minimize(dose_optimization_objective, initial_doses, args=(optimization_params,), bounds=bounds, method='L-BFGS-B')
    dose_phe_opt, dose_val_opt = result.x
    avg_risk_opt = run_simulation_scenario("optimized_dosing", clinical_params, dose_phe_opt, dose_val_opt, interval, duration)
    print(f"Optimized Dosing - Average Seizure Risk: {avg_risk_opt:.3f}")

    # Scenario 3: Higher Proteinuria (e.g., CL_protein_loss = 20.0)
    params_high_proteinuria = clinical_params.copy()
    params_high_proteinuria["CL_protein_loss"] = 20.0
    avg_risk_high_proteinuria_std = run_simulation_scenario("high_proteinuria_standard_dosing", params_high_proteinuria, dose_phe_standard, dose_val_standard, interval, duration)
    print(f"High Proteinuria (Standard Dosing) - Average Seizure Risk: {avg_risk_high_proteinuria_std:.3f}")
    result_high_proteinuria = minimize(dose_optimization_objective, initial_doses, args=({'params': params_high_proteinuria, 'interval': interval, 'duration': duration}), bounds=bounds, method='L-BFGS-B')
    dose_phe_opt_hp, dose_val_opt_hp = result_high_proteinuria.x
    avg_risk_high_proteinuria_opt = run_simulation_scenario("high_proteinuria_optimized_dosing", params_high_proteinuria, dose_phe_opt_hp, dose_val_opt_hp, interval, duration)
    print(f"High Proteinuria (Optimized Dosing) - Average Seizure Risk: {avg_risk_high_proteinuria_opt:.3f}")

    # Scenario 4: Reduced Albumin Replacement (e.g., k_alb_in = 10.0)
    params_low_alb_repl = clinical_params.copy()
    params_low_alb_repl["k_alb_in"] = 10.0
    avg_risk_low_alb_repl_std = run_simulation_scenario("low_albumin_replacement_standard_dosing", params_low_alb_repl, dose_phe_standard, dose_val_standard, interval, duration)
    print(f"Low Albumin Replacement (Standard Dosing) - Average Seizure Risk: {avg_risk_low_alb_repl_std:.3f}")
    result_low_alb_repl = minimize(dose_optimization_objective, initial_doses, args=({'params': params_low_alb_repl, 'interval': interval, 'duration': duration}), bounds=bounds, method='L-BFGS-B')
    dose_phe_opt_lar, dose_val_opt_lar = result_low_alb_repl.x
    avg_risk_low_alb_repl_opt = run_simulation_scenario("low_albumin_replacement_optimized_dosing", params_low_alb_repl, dose_phe_opt_lar, dose_val_opt_lar, interval, duration)
    print(f"Low Albumin Replacement (Optimized Dosing) - Average Seizure Risk: {avg_risk_low_alb_repl_opt:.3f}")

    print("\nAll scenario simulations complete. Results saved to CSV files.")


