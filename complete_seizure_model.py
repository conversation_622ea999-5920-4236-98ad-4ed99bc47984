import numpy as np
from scipy.integrate import odeint
import matplotlib.pyplot as plt
from scipy.optimize import minimize

def seizure_pharmacokinetic_model(state, t, params):
    """
    Complete ODE system for antiepileptic drug kinetics in proteinuric patient
    
    This model captures the key pathophysiology:
    1. Both drugs are highly protein-bound (>90% normally)
    2. Proteinuria causes loss of both free and bound drug
    3. Reduced albumin affects protein binding equilibrium
    4. Albumin replacement therapy partially compensates
    5. Need to maintain therapeutic brain concentrations for seizure control
    """
    
    # State variables: [phe_abs, phe_free, phe_bound, val_abs, val_free, val_bound, albumin, effect]
    A_phe_abs, A_phe_free, A_phe_bound, A_val_abs, A_val_free, A_val_bound, A_alb, Effect = state
    
    # Unpack parameters
    ka_phe = params["ka_phe"]          # Phenytoin absorption rate (/h)
    ka_val = params["ka_val"]          # Valproate absorption rate (/h)  
    Vd = params["Vd"]                  # Volume of distribution (L)
    CL_phe_normal = params["CL_phe_normal"]  # Normal phenytoin clearance (L/h)
    CL_val_normal = params["CL_val_normal"]  # Normal valproate clearance (L/h)
    CL_protein_loss = params["CL_protein_loss"]  # Enhanced clearance due to proteinuria (L/h)
    Kd_phe = params["Kd_phe"]          # Phenytoin-albumin dissociation constant (mg/L)
    Kd_val = params["Kd_val"]          # Valproate-albumin dissociation constant (mg/L)
    k_alb_in = params["k_alb_in"]      # Albumin replacement therapy rate (g/h)
    k_alb_loss = params["k_alb_loss"]  # Albumin loss rate due to proteinuria (/h)
    k_alb_syn = params["k_alb_syn"]    # Endogenous albumin synthesis rate (g/h)
    ke0 = params["ke0"]                # Effect compartment equilibration rate (/h)
    
    # Convert amounts to concentrations for calculations
    C_phe_free = A_phe_free / Vd if A_phe_free > 0 else 0
    C_val_free = A_val_free / Vd if A_val_free > 0 else 0
    C_alb = A_alb / Vd  # Albumin concentration (g/L)
    
    # Protein binding calculations using Langmuir model
    # When albumin is low, free fraction increases dramatically
    # This is a key insight: low albumin = more free drug initially, but then rapid loss
    f_free_phe = Kd_phe / (Kd_phe + C_alb * 1000)  # Convert albumin g/L to mg/L for binding
    f_free_val = Kd_val / (Kd_val + C_alb * 1000)
    
    # Ensure fractions are bounded [0,1]
    f_free_phe = max(0.01, min(0.99, f_free_phe))
    f_free_val = max(0.01, min(0.99, f_free_val))
    
    # Fast binding equilibrium assumption - binding/unbinding rates are much faster than elimination
    k_bind = 50  # Fast equilibration rate (/h)
    
    # Total drug amounts in central compartment
    A_phe_total = A_phe_free + A_phe_bound
    A_val_total = A_val_free + A_val_bound
    
    # Target amounts at binding equilibrium
    A_phe_free_target = A_phe_total * f_free_phe
    A_phe_bound_target = A_phe_total * (1 - f_free_phe)
    A_val_free_target = A_val_total * f_free_val
    A_val_bound_target = A_val_total * (1 - f_free_val)
    
    # Differential equations
    # ===================
    
    # Drug absorption compartments - first-order absorption
    dA_phe_abs_dt = -ka_phe * A_phe_abs
    dA_val_abs_dt = -ka_val * A_val_abs
    
    # Free drug compartments
    # Input: absorption + net unbinding
    # Output: normal elimination + proteinuria loss
    dA_phe_free_dt = (ka_phe * A_phe_abs + 
                      k_bind * (A_phe_free_target - A_phe_free) - 
                      (CL_phe_normal + CL_protein_loss) * C_phe_free)
    
    dA_val_free_dt = (ka_val * A_val_abs + 
                      k_bind * (A_val_free_target - A_val_free) - 
                      (CL_val_normal + CL_protein_loss) * C_val_free)
    
    # Bound drug compartments  
    # Input: net binding
    # Output: proteinuria loss (bound drug-protein complexes lost in urine)
    dA_phe_bound_dt = (k_bind * (A_phe_bound_target - A_phe_bound) - 
                       CL_protein_loss * (A_phe_bound / Vd))
    
    dA_val_bound_dt = (k_bind * (A_val_bound_target - A_val_bound) - 
                       CL_protein_loss * (A_val_bound / Vd))
    
    # Albumin dynamics
    # Input: IV replacement therapy + endogenous hepatic synthesis
    # Output: urinary loss due to glomerular damage
    dA_alb_dt = k_alb_in + k_alb_syn - k_alb_loss * A_alb
    
    # Effect compartment (brain tissue drug concentration)
    # Only free drug crosses blood-brain barrier effectively
    # Combined effect assumes additive pharmacodynamics (simplified)
    combined_free_conc = C_phe_free + C_val_free
    dEffect_dt = ke0 * (combined_free_conc - Effect)
    
    return [dA_phe_abs_dt, dA_phe_free_dt, dA_phe_bound_dt, 
            dA_val_abs_dt, dA_val_free_dt, dA_val_bound_dt,
            dA_alb_dt, dEffect_dt]

def simulate_multiple_doses(dose_phe, dose_val, interval, duration, params):
    """
    Simulate repeated dosing over specified duration
    
    This function handles the discrete dosing events overlaid on continuous ODEs
    """
    # Time vector with high resolution for smooth curves
    dt = 0.1  # 6-minute resolution
    t_points = np.arange(0, duration + dt, dt)
    
    # Initialize with normal albumin level (35 g/L * Vd)
    # All drug compartments start empty
    initial_state = [0, 0, 0, 0, 0, 0, 35 * params["Vd"], 0]
    
    # Dose times
    dose_times = np.arange(0, duration, interval)
    
    # Storage for results
    all_times = []
    all_states = []
    
    current_state = initial_state
    last_time = 0
    
    for i, dose_time in enumerate(dose_times):
        # Simulate from last time to current dose time
        if dose_time > last_time:
            t_segment = np.arange(last_time, dose_time + dt, dt)
            if len(t_segment) > 1:
                states = odeint(seizure_pharmacokinetic_model, current_state, 
                               t_segment, args=(params,))
                all_times.extend(t_segment[:-1])  # Exclude last point to avoid duplication
                all_states.extend(states[:-1])
                current_state = states[-1]
        
        # Add doses to absorption compartments
        current_state[0] += dose_phe  # Phenytoin absorption compartment
        current_state[3] += dose_val  # Valproate absorption compartment
        
        last_time = dose_time
    
    # Simulate remaining time after last dose
    if last_time < duration:
        t_final = np.arange(last_time, duration + dt, dt)
        if len(t_final) > 1:
            states = odeint(seizure_pharmacokinetic_model, current_state, 
                           t_final, args=(params,))
            all_times.extend(t_final)
            all_states.extend(states)
    
    return np.array(all_times), np.array(all_states)

def seizure_risk_score(effect_concentration, threshold=0.1, steepness=100):
    """
    Calculate seizure risk based on brain drug concentration
    
    Lower concentrations = higher seizure risk
    This sigmoid function models the threshold effect typical of antiepileptics
    """
    return 1 / (1 + np.exp(steepness * (effect_concentration - threshold)))

def dose_optimization_objective(doses, target_params):
    """
    Objective function for dose optimization
    
    Minimize seizure risk while avoiding toxicity
    """
    dose_phe, dose_val = doses
    params = target_params["params"]
    interval = target_params["interval"]
    duration = target_params["duration"]
    
    # Simulate with these doses
    times, states = simulate_multiple_doses(dose_phe, dose_val, interval, duration, params)
    
    # Extract effect compartment concentrations
    effect_conc = states[:, 7]  # Effect compartment is index 7
    
    # Calculate average seizure risk over simulation period
    seizure_risks = [seizure_risk_score(conc) for conc in effect_conc]
    avg_seizure_risk = np.mean(seizure_risks)
    
    # Penalty for very high doses (toxicity concern)
    toxicity_penalty = 0.001 * (max(0, dose_phe - 600)**2 + max(0, dose_val - 1000)**2)
    
    # Penalty for high peak concentrations
    max_free_phe = np.max(states[:, 1]) / params["Vd"]  # Max free phenytoin concentration
    max_free_val = np.max(states[:, 4]) / params["Vd"]  # Max free valproate concentration
    peak_penalty = 0.01 * (max(0, max_free_phe - 20)**2 + max(0, max_free_val - 100)**2)
    
    return avg_seizure_risk + toxicity_penalty + peak_penalty

# Model parameters based on literature and clinical estimates
clinical_params = {
    "ka_phe": 0.6,           # Phenytoin absorption rate (0.6 /h)
    "ka_val": 1.8,           # Valproate absorption rate (1.8 /h)
    "Vd": 50,                # Volume of distribution for 70kg patient (L)
    "CL_phe_normal": 2.2,    # Normal phenytoin clearance (L/h)
    "CL_val_normal": 4.5,    # Normal valproate clearance (L/h)
    "CL_protein_loss": 12.0, # Enhanced clearance due to proteinuria (L/h) - CRITICAL PARAMETER
    "Kd_phe": 8.0,           # Phenytoin-albumin binding constant (mg/L)
    "Kd_val": 45.0,          # Valproate-albumin binding constant (mg/L)
    "k_alb_in": 18.0,        # Albumin replacement rate (g/h)
    "k_alb_loss": 0.12,      # Albumin loss rate (/h)
    "k_alb_syn": 7.5,        # Endogenous albumin synthesis (g/h)
    "ke0": 0.25,             # Effect compartment equilibration (0.25 /h)
}

if __name__ == "__main__":
    # Example simulation comparing standard vs optimized dosing
    print("=== SEIZURE CONTROL MODEL FOR PROTEINURIC PATIENT ===")
    print()
    print("This model demonstrates how proteinuria dramatically affects")
    print("antiepileptic drug pharmacokinetics and guides dose optimization.")
    print()
    
    # Simulation parameters
    duration = 96  # 4 days
    interval = 12  # Every 12 hours
    
    # Standard dosing (what might be used normally)
    dose_phe_standard = 300  # mg
    dose_val_standard = 500  # mg
    
    print(f"Simulating {duration} hours with dosing every {interval} hours")
    print(f"Standard doses: Phenytoin {dose_phe_standard}mg, Valproate {dose_val_standard}mg")
    print()
    
    # Run standard dosing simulation
    t_std, states_std = simulate_multiple_doses(dose_phe_standard, dose_val_standard, 
                                               interval, duration, clinical_params)
    
    # Calculate key metrics for standard dosing
    effect_std = states_std[:, 7]
    albumin_std = states_std[:, 6] / clinical_params["Vd"]  # Convert to g/L
    phe_free_std = states_std[:, 1] / clinical_params["Vd"]  # mg/L
    val_free_std = states_std[:, 4] / clinical_params["Vd"]  # mg/L
    
    seizure_risk_std = [seizure_risk_score(conc) for conc in effect_std]
    avg_risk_std = np.mean(seizure_risk_std)
    
    print(f"Standard dosing results:")
    print(f"  Average seizure risk: {avg_risk_std:.3f}")
    print(f"  Average effect concentration: {np.mean(effect_std):.2f} mg/L")
    print(f"  Steady-state albumin: {np.mean(albumin_std[-200:]):.1f} g/L")
    print()
    
    # Now optimize doses
    print("Optimizing doses to minimize seizure risk...")
    
    optimization_params = {
        "params": clinical_params,
        "interval": interval,
        "duration": duration
    }
    
    # Initial guess - increase doses by 50%
    initial_doses = [450, 750]
    
    # Bounds: minimum 200mg, maximum 800mg for phenytoin; minimum 300mg, maximum 1200mg for valproate
    bounds = [(200, 800), (300, 1200)]
    
    result = minimize(dose_optimization_objective, initial_doses, 
                     args=(optimization_params,), bounds=bounds, method='L-BFGS-B')
    
    dose_phe_opt, dose_val_opt = result.x
    
    print(f"Optimized doses: Phenytoin {dose_phe_opt:.0f}mg, Valproate {dose_val_opt:.0f}mg")
    print(f"Dose increases: Phenytoin +{(dose_phe_opt/dose_phe_standard-1)*100:.0f}%, Valproate +{(dose_val_opt/dose_val_standard-1)*100:.0f}%")
    print()
    
    # Simulate optimized dosing
    t_opt, states_opt = simulate_multiple_doses(dose_phe_opt, dose_val_opt, 
                                               interval, duration, clinical_params)
    
    effect_opt = states_opt[:, 7]
    seizure_risk_opt = [seizure_risk_score(conc) for conc in effect_opt]
    avg_risk_opt = np.mean(seizure_risk_opt)
    
    print(f"Optimized dosing results:")
    print(f"  Average seizure risk: {avg_risk_opt:.3f}")
    print(f"  Risk reduction: {(1-avg_risk_opt/avg_risk_std)*100:.1f}%")
    print(f"  Average effect concentration: {np.mean(effect_opt):.2f} mg/L")
    print()
    
    print("=== KEY CLINICAL INSIGHTS ===")
    print("1. Proteinuria creates a \'double hit\': reduced protein binding AND enhanced elimination")
    print("2. Standard doses become inadequate due to massive drug losses")
    """3. Dose optimization must balance efficacy against toxicity"""
    print("4. Real-time monitoring of drug levels would be ideal for this patient")
    print("5. Consider switching to less protein-bound alternatives if available")


