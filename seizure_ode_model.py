import numpy as np
from scipy.integrate import odeint
import matplotlib.pyplot as plt

def seizure_pharmacokinetic_model(state, t, params):
    """
    ODE system modeling antiepileptic drug kinetics in a patient with proteinuria
    
    State variables:
    [0] A_phe_abs: Amount of phenytoin in absorption compartment (mg)
    [1] A_phe_free: Amount of free phenytoin in central compartment (mg)
    [2] A_phe_bound: Amount of bound phenytoin in central compartment (mg)
    [3] A_val_abs: Amount of valproate in absorption compartment (mg)
    [4] A_val_free: Amount of free valproate in central compartment (mg)
    [5] A_val_bound: Amount of bound valproate in central compartment (mg)
    [6] A_alb: Amount of albumin in plasma (g)
    [7] Effect: Effect compartment concentration (combined drug effect)
    
    This model accounts for:
    - Oral drug absorption
    - Protein binding equilibrium
    - Normal hepatic/renal elimination
    - Enhanced elimination due to proteinuria
    - Albumin replacement therapy
    - Pharmacodynamic effect on seizure threshold
    """
    
    # Unpack state variables
    A_phe_abs, A_phe_free, A_phe_bound, A_val_abs, A_val_free, A_val_bound, A_alb, Effect = state
    
    # Unpack parameters
    ka_phe = params['ka_phe']          # Phenytoin absorption rate (/h)
    ka_val = params['ka_val']          # Valproate absorption rate (/h)
    Vd = params['Vd']                  # Volume of distribution (L)
    CL_phe_normal = params['CL_phe_normal']  # Normal phenytoin clearance (L/h)
    CL_val_normal = params['CL_val_normal']  # Normal valproate clearance (L/h)
    CL_protein_loss = params['CL_protein_loss']  # Proteinuria clearance (L/h)
    Kd_phe = params['Kd_phe']          # Phenytoin-albumin dissociation constant (mg/L)
    Kd_val = params['Kd_val']          # Valproate-albumin dissociation constant (mg/L)
    k_alb_in = params['k_alb_in']      # Albumin replacement rate (g/h)
    k_alb_loss = params['k_alb_loss']  # Albumin loss rate (/h)
    k_alb_syn = params['k_alb_syn']    # Endogenous albumin synthesis (g/h)
    ke0 = params['ke0']                # Effect compartment equilibration rate (/h)
    
    # Calculate concentrations (mg/L)
    C_phe_free = A_phe_free / Vd
    C_val_free = A_val_free / Vd
    C_alb = A_alb / Vd  # g/L
    
    # Protein binding equilibrium - Langmuir binding model
    # For phenytoin: bound = (C_free * C_alb) / (Kd + C_free)
    # But we need to solve for equilibrium given total amounts
    
    # Simplified binding calculation assuming rapid equilibrium
    # Free fraction depends on albumin concentration and binding affinity
    f_free_phe = Kd_phe / (Kd_phe + C_alb * 1000)  # Convert albumin to mg/L for binding
    f_free_val = Kd_val / (Kd_val + C_alb * 1000)
    
    # Binding/unbinding rates (assuming rapid equilibrium, these should be large)
    k_bind = 100  # Fast binding rate (/h)
    
    # Total drug amounts
    A_phe_total = A_phe_free + A_phe_bound
    A_val_total = A_val_free + A_val_bound
    
    # Target bound and free amounts based on equilibrium
    A_phe_free_eq = A_phe_total * f_free_phe
    A_phe_bound_eq = A_phe_total * (1 - f_free_phe)
    A_val_free_eq = A_val_total * f_free_val
    A_val_bound_eq = A_val_total * (1 - f_free_val)
    
    # Drug absorption
    dA_phe_abs_dt = -ka_phe * A_phe_abs
    dA_val_abs_dt = -ka_val * A_val_abs
    
    # Free drug compartment
    # Input: absorption + unbinding, Output: normal elimination + proteinuria loss + binding
    dA_phe_free_dt = (ka_phe * A_phe_abs + 
                      k_bind * (A_phe_free_eq - A_phe_free) - 
                      (CL_phe_normal + CL_protein_loss) * C_phe_free)
    
    dA_val_free_dt = (ka_val * A_val_abs + 
                      k_bind * (A_val_free_eq - A_val_free) - 
                      (CL_val_normal + CL_protein_loss) * C_val_free)
    
    # Bound drug compartment  
    # Input: binding, Output: proteinuria loss + unbinding
    dA_phe_bound_dt = (k_bind * (A_phe_bound_eq - A_phe_bound) - 
                       CL_protein_loss * (A_phe_bound / Vd))
    
    dA_val_bound_dt = (k_bind * (A_val_bound_eq - A_val_bound) - 
                       CL_protein_loss * (A_val_bound / Vd))
    
    # Albumin dynamics
    # Input: replacement therapy + endogenous synthesis
    # Output: proteinuria loss
    dA_alb_dt = k_alb_in + k_alb_syn - k_alb_loss * A_alb
    
    # Effect compartment (represents drug effect in brain)
    # Combined effect of both free drug concentrations
    combined_free_conc = C_phe_free + C_val_free  # Simplified additive model
    dEffect_dt = ke0 * (combined_free_conc - Effect)
    
    return [dA_phe_abs_dt, dA_phe_free_dt, dA_phe_bound_dt, 
            dA_val_abs_dt, dA_val_free_dt, dA_val_bound_dt,
            dA_alb_dt, dEffect_dt]

def seizure_probability(effect, threshold=5.0, steepness=2.0):
    """
    Calculate probability of seizure based on effect compartment concentration
    Lower drug effect = higher seizure probability
    """
    return 1 / (1 + np.exp(steepness * (effect - threshold)))

def simulate_dosing_regimen(dose_phe, dose_val, dosing_interval, duration, params):
    """
    Simulate multiple dose administration over time
    """
    t_total = np.linspace(0, duration, int(duration * 10))  # 0.1 h resolution
    
    # Initial conditions
    initial_state = [0, 0, 0, 0, 0, 0, 35*params['Vd'], 0]  # Start with normal albumin
    
    # Arrays to store results
    all_states = []
    dose_times = np.arange(0, duration, dosing_interval)
    
    current_state = initial_state
    current_time = 0
    
    for i, dose_time in enumerate(dose_times):
        if i < len(dose_times) - 1:
            next_dose_time = dose_times[i + 1]
        else:
            next_dose_time = duration
            
        # Time points until next dose
        t_segment = np.linspace(current_time, next_dose_time, 
                               int((next_dose_time - current_time) * 10))
        
        # Solve ODE for this segment
        segment_states = odeint(seizure_pharmacokinetic_model, current_state, 
                               t_segment, args=(params,))
        
        all_states.append(segment_states)
        
        # Add dose at the end of segment (if not the last segment)
        if i < len(dose_times) - 1:
            current_state = segment_states[-1].copy()
            current_state[0] += dose_phe  # Add phenytoin dose to absorption compartment
            current_state[3] += dose_val  # Add valproate dose to absorption compartment
        
        current_time = next_dose_time
    
    # Concatenate all segments
    all_states = np.vstack(all_states)
    
    return t_total[:len(all_states)], all_states

# Example parameters based on literature values and clinical scenario
params = {
    'ka_phe': 0.5,           # Phenytoin absorption rate (0.5 /h)
    'ka_val': 2.0,           # Valproate absorption rate (2.0 /h) - faster than phenytoin
    'Vd': 50,                # Volume of distribution (L) for 70kg patient
    'CL_phe_normal': 2.5,    # Normal phenytoin clearance (L/h)
    'CL_val_normal': 5.0,    # Normal valproate clearance (L/h)
    'CL_protein_loss': 15.0, # Enhanced clearance due to proteinuria (L/h)
    'Kd_phe': 10.0,          # Phenytoin binding dissociation constant (mg/L)
    'Kd_val': 50.0,          # Valproate binding dissociation constant (mg/L)
    'k_alb_in': 20.0,        # Albumin replacement rate (g/h)
    'k_alb_loss': 0.15,      # Albumin loss rate due to proteinuria (/h)
    'k_alb_syn': 8.0,        # Endogenous albumin synthesis (g/h)
    'ke0': 0.2,              # Effect compartment equilibration (0.2 /h)
}

# Simulation example
print("Antiepileptic Drug Optimization Model for Proteinuric Patient")
print("=" * 60)
print("\nThis model considers:")
print("- Oral drug absorption kinetics")
print("- Protein binding equilibrium with albumin")
print("- Normal hepatic/renal elimination")
print("- Enhanced drug loss due to proteinuria")
print("- Albumin replacement therapy")
print("- Combined pharmacodynamic effect")
print("\nKey assumptions:")
print("- Rapid protein binding equilibrium")
print("- Additive drug effects")
print("- Linear pharmacokinetics")
print("- Constant proteinuria rate")

# Example simulation - compare normal vs increased dosing
duration = 72  # 72 hours simulation
dosing_interval = 12  # Every 12 hours

# Standard doses
dose_phe_standard = 300  # mg
dose_val_standard = 500  # mg

# Optimized doses (higher to compensate for losses)
dose_phe_optimized = 450  # mg
dose_val_optimized = 750  # mg

print(f"\nSimulation parameters:")
print(f"- Duration: {duration} hours")
print(f"- Dosing interval: {dosing_interval} hours")
print(f"- Standard doses: Phenytoin {dose_phe_standard}mg, Valproate {dose_val_standard}mg")
print(f"- Optimized doses: Phenytoin {dose_phe_optimized}mg, Valproate {dose_val_optimized}mg")
print(f"- Enhanced clearance due to proteinuria: {params['CL_protein_loss']} L/h")
