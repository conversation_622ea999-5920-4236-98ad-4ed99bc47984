import pandas as pd
import numpy as np

def seizure_risk_score(effect_concentration, threshold=0.1, steepness=100):
    return 1 / (1 + np.exp(steepness * (effect_concentration - threshold)))

def generate_summary_table():
    scenarios = {
        "standard_dosing": {"name": "Standard Dosing", "dose_phe": 300, "dose_val": 500},
        "optimized_dosing": {"name": "Optimized Dosing", "dose_phe": 450, "dose_val": 750},
        "high_proteinuria_standard_dosing": {"name": "High Proteinuria (Standard Dosing)", "dose_phe": 300, "dose_val": 500},
        "high_proteinuria_optimized_dosing": {"name": "High Proteinuria (Optimized Dosing)", "dose_phe": 450, "dose_val": 750},
        "low_albumin_replacement_standard_dosing": {"name": "Low Albumin Replacement (Standard Dosing)", "dose_phe": 300, "dose_val": 500},
        "low_albumin_replacement_optimized_dosing": {"name": "Low Albumin Replacement (Optimized Dosing)", "dose_phe": 450, "dose_val": 750},
    }

    results = []

    for scenario_key, scenario_info in scenarios.items():
        df = pd.read_csv(f"results_{scenario_key}.csv")

        avg_seizure_risk = np.mean([seizure_risk_score(conc) for conc in df["Effect_Concentration"]])
        avg_effect_conc = np.mean(df["Effect_Concentration"])
        avg_albumin_conc = np.mean(df["Albumin_Concentration"])
        avg_phe_free_conc = np.mean(df["Phenytoin_Free_Concentration"])
        avg_val_free_conc = np.mean(df["Valproate_Free_Concentration"])
        avg_phe_total_conc = np.mean(df["Phenytoin_Total_Concentration"])
        avg_val_total_conc = np.mean(df["Valproate_Total_Concentration"])

        results.append({
            "Scenario": scenario_info["name"],
            "Phenytoin Dose (mg)": scenario_info["dose_phe"],
            "Valproate Dose (mg)": scenario_info["dose_val"],
            "Avg Seizure Risk": f"{avg_seizure_risk:.3f}",
            "Avg Effect Conc (mg/L)": f"{avg_effect_conc:.2f}",
            "Avg Albumin Conc (g/L)": f"{avg_albumin_conc:.1f}",
            "Avg Phenytoin Free Conc (mg/L)": f"{avg_phe_free_conc:.2f}",
            "Avg Valproate Free Conc (mg/L)": f"{avg_val_free_conc:.2f}",
            "Avg Phenytoin Total Conc (mg/L)": f"{avg_phe_total_conc:.2f}",
            "Avg Valproate Total Conc (mg/L)": f"{avg_val_total_conc:.2f}",
        })

    summary_df = pd.DataFrame(results)
    summary_df.to_csv("simulation_summary_table.csv", index=False)
    print("Simulation summary table generated successfully.")

if __name__ == "__main__":
    generate_summary_table()


