import numpy as np
from scipy.integrate import odeint
from scipy.optimize import minimize

# Copy the relevant functions from complete_seizure_model.py
def seizure_pharmacokinetic_model(state, t, params):
    A_phe_abs, A_phe_free, A_phe_bound, A_val_abs, A_val_free, A_val_bound, A_alb, Effect = state
    ka_phe = params["ka_phe"]
    ka_val = params["ka_val"]
    Vd = params["Vd"]
    CL_phe_normal = params["CL_phe_normal"]
    CL_val_normal = params["CL_val_normal"]
    CL_protein_loss = params["CL_protein_loss"]
    Kd_phe = params["Kd_phe"]
    Kd_val = params["Kd_val"]
    k_alb_in = params["k_alb_in"]
    k_alb_loss = params["k_alb_loss"]
    k_alb_syn = params["k_alb_syn"]
    ke0 = params["ke0"]
    C_phe_free = A_phe_free / Vd if A_phe_free > 0 else 0
    C_val_free = A_val_free / Vd if A_val_free > 0 else 0
    C_alb = A_alb / Vd
    f_free_phe = Kd_phe / (Kd_phe + C_alb * 1000)
    f_free_val = Kd_val / (Kd_val + C_alb * 1000)
    f_free_phe = max(0.01, min(0.99, f_free_phe))
    f_free_val = max(0.01, min(0.99, f_free_val))
    k_bind = 50
    A_phe_total = A_phe_free + A_phe_bound
    A_val_total = A_val_free + A_val_bound
    A_phe_free_target = A_phe_total * f_free_phe
    A_phe_bound_target = A_phe_total * (1 - f_free_phe)
    A_val_free_target = A_val_total * f_free_val
    A_val_bound_target = A_val_total * (1 - f_free_val)
    dA_phe_abs_dt = -ka_phe * A_phe_abs
    dA_val_abs_dt = -ka_val * A_val_abs
    dA_phe_free_dt = (ka_phe * A_phe_abs + k_bind * (A_phe_free_target - A_phe_free) - (CL_phe_normal + CL_protein_loss) * C_phe_free)
    dA_val_free_dt = (ka_val * A_val_abs + k_bind * (A_val_free_target - A_val_free) - (CL_val_normal + CL_protein_loss) * C_val_free)
    dA_phe_bound_dt = (k_bind * (A_phe_bound_target - A_phe_bound) - CL_protein_loss * (A_phe_bound / Vd))
    dA_val_bound_dt = (k_bind * (A_val_bound_target - A_val_bound) - CL_protein_loss * (A_val_bound / Vd))
    dA_alb_dt = k_alb_in + k_alb_syn - k_alb_loss * A_alb
    combined_free_conc = C_phe_free + C_val_free
    dEffect_dt = ke0 * (combined_free_conc - Effect)
    return [dA_phe_abs_dt, dA_phe_free_dt, dA_phe_bound_dt, dA_val_abs_dt, dA_val_free_dt, dA_val_bound_dt, dA_alb_dt, dEffect_dt]

def simulate_multiple_doses(dose_phe, dose_val, interval, duration, params):
    dt = 0.1
    t_points = np.arange(0, duration + dt, dt)
    initial_state = [0, 0, 0, 0, 0, 0, 35 * params["Vd"], 0]
    dose_times = np.arange(0, duration, interval)
    all_times = []
    all_states = []
    current_state = initial_state
    last_time = 0
    for i, dose_time in enumerate(dose_times):
        if dose_time > last_time:
            t_segment = np.arange(last_time, dose_time + dt, dt)
            if len(t_segment) > 1:
                states = odeint(seizure_pharmacokinetic_model, current_state, t_segment, args=(params,))
                all_times.extend(t_segment[:-1])
                all_states.extend(states[:-1])
                current_state = states[-1]
        current_state[0] += dose_phe
        current_state[3] += dose_val
        last_time = dose_time
    if last_time < duration:
        t_final = np.arange(last_time, duration + dt, dt)
        if len(t_final) > 1:
            states = odeint(seizure_pharmacokinetic_model, current_state, t_final, args=(params,))
            all_times.extend(t_final)
            all_states.extend(states)
    return np.array(all_times), np.array(all_states)

def seizure_risk_score(effect_concentration, threshold=8.0, steepness=1.5):
    return 1 / (1 + np.exp(steepness * (effect_concentration - threshold)))

# Model parameters
clinical_params = {
    'ka_phe': 0.6,
    'ka_val': 1.8,
    'Vd': 50,
    'CL_phe_normal': 2.2,
    'CL_val_normal': 4.5,
    'CL_protein_loss': 12.0,
    'Kd_phe': 8.0,
    'Kd_val': 45.0,
    'k_alb_in': 18.0,
    'k_alb_loss': 0.12,
    'k_alb_syn': 7.5,
    'ke0': 0.25,
}

# Simulation parameters
duration = 96
interval = 12
dose_phe_standard = 300
dose_val_standard = 500

# Run standard dosing simulation
t_std, states_std = simulate_multiple_doses(dose_phe_standard, dose_val_standard, interval, duration, clinical_params)

effect_std = states_std[:, 7]
print(f"Min effect concentration (standard): {np.min(effect_std):.2f}")
print(f"Max effect concentration (standard): {np.max(effect_std):.2f}")
print(f"Mean effect concentration (standard): {np.mean(effect_std):.2f}")

seizure_risks_std = [seizure_risk_score(conc) for conc in effect_std]
print(f"Min seizure risk (standard): {np.min(seizure_risks_std):.3f}")
print(f"Max seizure risk (standard): {np.max(seizure_risks_std):.3f}")
print(f"Mean seizure risk (standard): {np.mean(seizure_risks_std):.3f}")

# Optimized doses from previous run
dose_phe_opt = 450
dose_val_opt = 750

t_opt, states_opt = simulate_multiple_doses(dose_phe_opt, dose_val_opt, interval, duration, clinical_params)

effect_opt = states_opt[:, 7]
print(f"Min effect concentration (optimized): {np.min(effect_opt):.2f}")
print(f"Max effect concentration (optimized): {np.max(effect_opt):.2f}")
print(f"Mean effect concentration (optimized): {np.mean(effect_opt):.2f}")

seizure_risks_opt = [seizure_risk_score(conc) for conc in effect_opt]
print(f"Min seizure risk (optimized): {np.min(seizure_risks_opt):.3f}")
print(f"Max seizure risk (optimized): {np.max(seizure_risks_opt):.3f}")
print(f"Mean seizure risk (optimized): {np.mean(seizure_risks_opt):.3f}")


