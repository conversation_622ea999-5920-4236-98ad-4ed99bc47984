import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def plot_scenario_results(scenario_name, title_prefix):
    df = pd.read_csv(f"results_{scenario_name}.csv")

    fig, axs = plt.subplots(4, 1, figsize=(10, 12), sharex=True)
    fig.suptitle(f'{title_prefix} Simulation Results', fontsize=16)

    # Plot 1: Phenytoin Concentrations
    axs[0].plot(df['Time'], df['Phenytoin_Free_Concentration'], label='Phenytoin Free')
    axs[0].plot(df['Time'], df['Phenytoin_Total_Concentration'], label='Phenytoin Total', linestyle='--')
    axs[0].set_ylabel('Concentration (mg/L)')
    axs[0].set_title('Phenytoin Concentration Over Time')
    axs[0].legend()
    axs[0].grid(True)

    # Plot 2: Valproate Concentrations
    axs[1].plot(df['Time'], df['Valproate_Free_Concentration'], label='Valproate Free')
    axs[1].plot(df['Time'], df['Valproate_Total_Concentration'], label='Valproate Total', linestyle='--')
    axs[1].set_ylabel('Concentration (mg/L)')
    axs[1].set_title('Valproate Concentration Over Time')
    axs[1].legend()
    axs[1].grid(True)

    # Plot 3: Albumin Concentration
    axs[2].plot(df['Time'], df['Albumin_Concentration'], label='Albumin')
    axs[2].set_ylabel('Concentration (g/L)')
    axs[2].set_title('Albumin Concentration Over Time')
    axs[2].legend()
    axs[2].grid(True)

    # Plot 4: Effect Concentration and Seizure Risk
    ax4_1 = axs[3]
    ax4_1.plot(df['Time'], df['Effect_Concentration'], label='Effect Concentration', color='green')
    ax4_1.set_ylabel('Effect Concentration (mg/L)', color='green')
    ax4_1.tick_params(axis='y', labelcolor='green')
    ax4_1.set_title('Effect Concentration and Seizure Risk Over Time')
    ax4_1.grid(True)

    ax4_2 = ax4_1.twinx()
    ax4_2.plot(df['Time'], df['Seizure_Risk'], label='Seizure Risk', color='red', linestyle=':')
    ax4_2.set_ylabel('Seizure Risk', color='red')
    ax4_2.tick_params(axis='y', labelcolor='red')
    ax4_2.legend(loc='upper right')

    plt.xlabel('Time (hours)')
    plt.tight_layout(rect=[0, 0.03, 1, 0.96])
    plt.savefig(f'{scenario_name}_results.png')
    plt.close(fig)

if __name__ == "__main__":
    plot_scenario_results('standard_dosing', 'Standard Dosing')
    plot_scenario_results('optimized_dosing', 'Optimized Dosing')
    plot_scenario_results('high_proteinuria_standard_dosing', 'High Proteinuria (Standard Dosing)')
    plot_scenario_results('high_proteinuria_optimized_dosing', 'High Proteinuria (Optimized Dosing)')
    plot_scenario_results('low_albumin_replacement_standard_dosing', 'Low Albumin Replacement (Standard Dosing)')
    plot_scenario_results('low_albumin_replacement_optimized_dosing', 'Low Albumin Replacement (Optimized Dosing)')

    print("All plots generated successfully.")


